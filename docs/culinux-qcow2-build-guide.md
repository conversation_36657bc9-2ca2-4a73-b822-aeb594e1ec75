# CULinux 4.0 qcow2 镜像构建文档

## 概述

本文档介绍如何使用 virt-install 手动安装方式构建 CULinux 4.0 qcow2 镜像文件。


## 核心步骤

### 1. 环境准备

确认宿主机环境：
```bash
# 检查 KVM 支持
lscpu | grep -i virtualization
ls -la /dev/kvm

# 检查 QEMU 版本
/usr/libexec/qemu-kvm --version

```

### 2. 创建虚拟磁盘

```bash
# 创建 qcow2 磁盘文件
qemu-img create -f qcow2 culinux-4.0-base.qcow2 20G
```

### 3. 启动虚拟机安装

使用经过验证的稳定硬件配置启动虚拟机进行手动安装：

**推荐配置：**
```bash
virt-install \
  --virt-type kvm \
  --name culinux-4.0-build \
  --ram 4096 \
  --vcpus 4 \
  --disk culinux-4.0-base.qcow2,format=qcow2,bus=ide \
  --network network=default,model=e1000 \
  --graphics vnc,listen=0.0.0.0 \
  --machine pc-i440fx-6.2 \
  --serial pty \
  --console pty,target_type=serial \
  --video vga \
  --noautoconsole \
  --os-type=linux \
  --os-variant=generic \
  --cdrom=/opt/packer-base-image/os/CULinux-4.0-kr720.x86_64.iso
  


```

**关键配置说明：**
- `--machine pc-i440fx-6.2`：使用兼容的机器类型，避免kernel panic
- `--disk bus=ide`：使用IDE总线，兼容性更好
- `--network model=e1000`：使用e1000网卡模型，驱动支持更好
- **网络自动配置**：虚拟机创建后会自动获得网络接口，无需手动配置
- `--serial pty`：配置串口控制台支持

### 4. 连接安装界面

虚拟机启动后，通过 VNC 连接到图形安装界面：

```bash
# 查看虚拟机状态
virsh list --all

# 查看 VNC 端口分配
virsh vncdisplay culinux-4.0-build

# 连接 VNC（假设显示 :0，对应端口 5900）
vncviewer <服务器IP>:5900
```

**连接验证：**
- VNC 连接成功后应该看到 CULinux 4.0 启动界面
- 选择 "Install CULinux 4.0" 进入安装程序
- 如果看不到界面，检查 VNC 端口和防火墙设置

**网络状态确认：**
- 虚拟机启动时，libvirt 会自动为其分配网络接口
- 在安装界面的 "网络和主机名" 部分可以看到网卡状态为 "已连接"
- 网络配置通过 DHCP 自动完成，无需手动设置 IP 地址

### 5. 手动安装配置

#### 启动安装程序

通过 VNC 连接到虚拟机后，会看到 CULinux 4.0 安装界面。选择 "Install CULinux 4.0" 进入安装。

#### 安装配置界面

安装程序启动后会进入图形化配置界面，主要包含以下配置项：

**1. 网络和主机名**
- ✅ **网络配置**：系统会自动检测并配置网络接口
- 虚拟机创建时已自动分配网卡，通常显示为 "已连接" 状态
- 可以设置主机名，建议保持默认或设为 `culinux`

**2. 软件选择**
- 选择 **最小安装** 或 **服务器安装**
- 建议选择最小安装以减小镜像大小
- 可以勾选 "开发工具" 如果需要编译环境

**3. 安装目的地（磁盘分区）**
- 点击进入磁盘分区配置
- 选择目标磁盘（显示为创建的 qcow2 文件大小）
- 推荐使用 **自动分区**，系统会自动创建合理的分区方案：
  ```
  /boot/efi  200MB   FAT32 (如果是UEFI)
  /boot      1GB     XFS
  swap       2GB     swap
  /          剩余空间  XFS (LVM)
  ```
- 高级用户可选择手动分区进行自定义

**4. 时间和日期**
- 设置时区为 **Asia/Shanghai**
- 可以启用网络时间同步

**5. 键盘布局**
- 保持默认 US 布局或根据需要调整

**6. 语言支持**
- 可以添加中文支持，但建议最小安装时保持英文

**7. 用户设置**
- **Root 密码**：设置为 `Culinux4@2025`（或根据安全策略设置）
- **创建用户**：可选择创建普通用户或仅使用 root
- ✅ 确保启用 root 账户SSH登录

#### 开始安装

确认所有配置项都已正确设置后（界面上不应有警告标志），点击 **"开始安装"** 按钮。

安装过程大约需要 10-20 分钟，取决于选择的软件包数量和硬件性能。

### 6. 安装后配置

安装完成重启后，登录系统进行基本配置：

```bash
# 更新系统
yum update -y

# 安装云计算相关包
yum install -y cloud-init cloud-utils-growpart acpid

# 启用必要服务
systemctl enable sshd
systemctl enable acpid
systemctl enable cloud-init
systemctl enable cloud-config
systemctl enable cloud-final
systemctl enable cloud-init-local

# 配置防火墙（推荐禁用用于镜像制作）
systemctl stop firewalld
systemctl disable firewalld

# 配置SELinux（推荐禁用）
sed -i 's/^SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config
sed -i 's/^SELINUX=permissive/SELINUX=disabled/' /etc/selinux/config
```

### 7. 串口控制台配置

为支持云平台的控制台访问，需要配置串口控制台：

```bash
# 1. 配置安全终端
grep -q ttyS0 /etc/securetty || echo "ttyS0" >> /etc/securetty

# 2. 配置GRUB添加串口控制台参数
vi /etc/default/grub
# 在GRUB_CMDLINE_LINUX行末尾添加: console=tty0 console=ttyS0,115200

# 3. 重新生成GRUB配置
grub2-mkconfig -o /boot/grub2/grub.cfg

# 4. 启用串口登录服务
<NAME_EMAIL>
<NAME_EMAIL>
```
 - disk_setup
 - mounts
### 8. 清理和优化

安装配置完成后，进行清理工作：

```bash
# 清理缓存
yum clean all
rm -rf /var/cache/yum/*

# 清理日志
> /var/log/messages
> /var/log/secure
> /var/log/cron

# 清理临时文件
rm -rf /tmp/*
rm -rf /var/tmp/*

# 清理网络配置（避免MAC地址冲突）
rm -f /etc/udev/rules.d/70-persistent-net.rules

# 清理cloud-init状态（确保镜像首次启动正常初始化）
cloud-init clean --logs

# 关机
shutdown -h now
```

### 9. 验证镜像

```bash
# 查看镜像信息
qemu-img info culinux-4.0-base.qcow2

# 测试启动
/usr/libexec/qemu-kvm \
  -m 2048 \
  -smp 2 \
  -drive file=culinux-4.0-base.qcow2,format=qcow2,if=virtio \
  -netdev user,id=net0 \
  -device virtio-net,netdev=net0 \
  -vnc 0.0.0.0:9 \
  -daemonize
```

## 故障排除

### 1. 硬件兼容性问题

**问题**：虚拟机启动时出现内核恐慌。

**解决方案**：
- 检查机器类型和CPU配置
- 使用兼容的硬件配置参数

### 2. 串口控制台问题

**问题**：`virsh console` 连接后无法看到登录界面。

**解决方案**：
```bash
# 检查串口控制台配置
/usr/local/bin/check-serial-console.sh

# 手动修复
grep -q ttyS0 /etc/securetty || echo "ttyS0" >> /etc/securetty
<NAME_EMAIL>
<NAME_EMAIL>
```

### 3. 防火墙SSH问题

**问题**：无法通过SSH连接虚拟机。

**解决方案**：
```bash
# 方法1：临时禁用防火墙
systemctl stop firewalld

# 方法2：配置防火墙规则
firewall-cmd --permanent --add-service=ssh
firewall-cmd --reload
```
  # 方法1：临时禁用防火墙（测试用）
  systemctl stop firewalld
  systemctl disable firewalld
  
  # 方法2：配置防火墙允许自定义端口（生产环境推荐）
  firewall-cmd --permanent --add-port=59599/tcp
  firewall-cmd --reload
  
  # 验证规则
  firewall-cmd --list-all
  ```
- **SSH配置验证**：
  ```bash
  # 检查SSH服务状态
  systemctl status sshd
  
  # 检查端口监听
### 4. 网络配置问题

**问题**：无法通过SSH连接虚拟机。

**解决方案**：
```bash
# 检查防火墙状态
systemctl status firewalld

# 临时禁用防火墙
systemctl stop firewalld

# 配置防火墙规则（如果需要保持防火墙开启）
firewall-cmd --permanent --add-service=ssh
firewall-cmd --reload
```

## 最佳实践

### 1. 硬件配置选择
- **机器类型**：使用 `pc-i440fx-6.2` 确保最佳兼容性
- **网卡类型**：使用 `e1000` 模型，驱动支持稳定
- **磁盘接口**：使用 `ide` 总线避免驱动问题
- **内存配置**：建议4GB以上，确保安装过程流畅

### 2. 网络配置
- **自动配置**：virt-install 创建虚拟机时会自动配置网络接口
- **默认网络**：使用 `network=default` 连接到 libvirt 默认网桥
- **网卡状态**：安装界面会显示网卡为 "已连接" 状态
- **无需手动配置**：DHCP 自动分配 IP 地址，无需手动设置

### 3. 安装流程
- **分步验证**：每个配置步骤都进行验证，确保无警告标志
- **最小安装**：选择最小安装减小镜像体积
- **用户配置**：建议只设置 root 用户，简化镜像配置
- **安全设置**：设置强密码，但在清理阶段会被重置

### 4. 手动安装的优势
- **可视化操作**：通过图形界面可以清楚看到各项配置状态
- **实时调整**：发现问题可以立即修改配置
- **网络自动化**：不需要手动配置网络参数
- **兼容性验证**：可以在安装过程中验证硬件兼容性

## 总结

使用 virt-install 手动安装方式构建 CULinux 4.0 qcow2 镜像的核心要点：

### 关键成功因素
1. **硬件兼容性**：使用经验证的硬件配置（pc-i440fx-6.2 + e1000 + ide）
2. **网络自动化**：依赖 libvirt 的自动网络配置，减少手动错误
3. **图形化安装**：通过 VNC 进行可视化配置，确保每步正确
4. **最小化安装**：选择最小安装包集合，减小镜像体积
5. **完整清理**：安装后进行彻底清理，确保镜像的通用性

### 手动安装工作流程
```
创建磁盘 → 启动虚拟机 → VNC连接 → 图形化配置 → 安装系统 → 
安装后配置 → 串口控制台 → 系统清理 → 关机导出
```

### 与自动化安装的区别
- **手动安装**：图形界面、网络自动配置、实时调整、稳定可靠
- **自动化安装**：无人值守、需要预配置网络、适合批量生产

手动安装方式特别适合：
- 首次构建 CULinux 4.0 镜像
- 验证新的硬件配置
- 排查兼容性问题
- 生产环境的稳定构建

## 附录

### 有用的调试命令

```bash
# 查看虚拟机状态
virsh list --all
virsh domstate <vm-name>

# 查看虚拟机配置
virsh dumpxml <vm-name>

# 查看 VNC 连接信息
virsh vncdisplay <vm-name>

# 强制关闭虚拟机
virsh destroy <vm-name>

# 删除虚拟机定义
virsh undefine <vm-name>
```
