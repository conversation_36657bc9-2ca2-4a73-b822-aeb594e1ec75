{
  "editor.unicodeHighlight.ambiguousCharacters": false,
  "editor.unicodeHighlight.invisibleCharacters": false,
  "editor.unicodeHighlight.nonBasicASCII": false,
  "editor.unicodeHighlight.includeComments": false,
  "editor.unicodeHighlight.includeStrings": false,

  // 完全禁用内联建议，避免与中文输入法冲突
  "editor.inlineSuggest.enabled": false,
  "editor.inlineSuggest.delay": 2000,

  // 禁用所有AI助手在文档文件中的建议
  "github.copilot.enable": {
    "*": true,
    "markdown": false,
    "plaintext": false,
    "text": false
  },
  "augment.enable": {
    "*": true,
    "markdown": false,
    "plaintext": false,
    "text": false
  },

  // 完全禁用快速建议
  "editor.quickSuggestions": {
    "other": false,
    "comments": false,
    "strings": false
  },
  "editor.quickSuggestionsDelay": 2000,

  // 禁用参数提示
  "editor.parameterHints.enabled": false,

  // 禁用自动补全
  "editor.suggestOnTriggerCharacters": false,
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "off",

  // 禁用词汇建议
  "editor.wordBasedSuggestions": "off"
}