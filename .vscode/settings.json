{
  // 禁用所有可能影响输入流畅度的功能
  "editor.inlineSuggest.enabled": false,
  "editor.quickSuggestions": false,
  "editor.parameterHints.enabled": false,
  "editor.suggestOnTriggerCharacters": false,
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "off",
  "editor.wordBasedSuggestions": "off",

  // 禁用所有AI助手
  "augment.enable": {
    "*": false
  },
  "github.copilot.enable": {
    "*": false
  },

  // 性能优化 - 禁用耗费资源的功能
  "editor.semanticHighlighting.enabled": false,
  "editor.bracketPairColorization.enabled": false,
  "editor.guides.indentation": false,
  "editor.renderLineHighlight": "none",
  "editor.occurrencesHighlight": "off",
  "editor.selectionHighlight": false,
  "editor.smoothScrolling": false,
  "editor.cursorSmoothCaretAnimation": "off",
  "editor.minimap.enabled": false,

  // Unicode 相关禁用
  "editor.unicodeHighlight.ambiguousCharacters": false,
  "editor.unicodeHighlight.invisibleCharacters": false,
  "editor.unicodeHighlight.nonBasicASCII": false,
  "editor.unicodeHighlight.includeComments": false,
  "editor.unicodeHighlight.includeStrings": false,

  // Markdown 专用配置 - 最大化性能
  "[markdown]": {
    "editor.wordWrap": "on",
    "editor.lineNumbers": "off",
    "editor.minimap.enabled": false,
    "editor.renderWhitespace": "none",
    "editor.renderControlCharacters": false,
    "editor.folding": false,
    "editor.glyphMargin": false,
    "editor.lightbulb.enabled": "off",
    "editor.hover.enabled": false,
    "editor.links": false,
    "editor.colorDecorators": false,
    "editor.codeLens": false,
    "files.trimTrailingWhitespace": false
  },

  // 文件监控优化
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/tmp/**": true
  }
}