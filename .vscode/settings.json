{"editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.invisibleCharacters": false, "editor.unicodeHighlight.nonBasicASCII": false, "editor.unicodeHighlight.includeComments": false, "editor.unicodeHighlight.includeStrings": false, "editor.inlineSuggest.delay": 500, "github.copilot.enable": {"*": true, "markdown": false, "plaintext": false}, "editor.quickSuggestions": {"other": true, "comments": false, "strings": false}}